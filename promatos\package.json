{"name": "promatos", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/bottom-tabs": "^7.4.6", "@react-navigation/native": "^7.1.17", "@react-navigation/stack": "^7.4.7", "@reduxjs/toolkit": "^2.8.2", "expo": "~53.0.20", "expo-av": "^15.1.7", "expo-camera": "^16.1.11", "expo-image-picker": "^16.1.4", "expo-location": "^18.1.6", "expo-permissions": "^14.4.0", "expo-speech": "^13.1.7", "expo-sqlite": "^15.2.14", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "^2.28.0", "react-native-maps": "^1.25.3", "react-native-reanimated": "^4.0.2", "react-native-safe-area-context": "^5.6.0", "react-native-screens": "^4.13.1", "react-native-vector-icons": "^10.3.0", "react-redux": "^9.2.0", "redux-persist": "^6.0.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}