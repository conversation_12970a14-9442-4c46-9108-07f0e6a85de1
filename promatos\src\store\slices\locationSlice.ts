import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import * as Location from 'expo-location';

export interface LocationCoordinates {
  latitude: number;
  longitude: number;
  accuracy?: number;
  altitude?: number;
  heading?: number;
  speed?: number;
}

export interface LocationState {
  currentLocation: LocationCoordinates | null;
  permissionStatus: 'granted' | 'denied' | 'undetermined';
  isTracking: boolean;
  lastUpdated: string | null;
  error: string | null;
  nearbyProfessionals: any[];
  searchRadius: number; // in kilometers
}

const initialState: LocationState = {
  currentLocation: null,
  permissionStatus: 'undetermined',
  isTracking: false,
  lastUpdated: null,
  error: null,
  nearbyProfessionals: [],
  searchRadius: 10,
};

// Async thunks for location operations
export const requestLocationPermission = createAsyncThunk(
  'location/requestPermission',
  async () => {
    const { status } = await Location.requestForegroundPermissionsAsync();
    return status;
  }
);

export const getCurrentLocation = createAsyncThunk(
  'location/getCurrentLocation',
  async (_, { rejectWithValue }) => {
    try {
      const { status } = await Location.getForegroundPermissionsAsync();
      if (status !== 'granted') {
        throw new Error('Location permission not granted');
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });

      return {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        accuracy: location.coords.accuracy,
        altitude: location.coords.altitude,
        heading: location.coords.heading,
        speed: location.coords.speed,
      };
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

export const startLocationTracking = createAsyncThunk(
  'location/startTracking',
  async (_, { dispatch }) => {
    const subscription = await Location.watchPositionAsync(
      {
        accuracy: Location.Accuracy.Balanced,
        timeInterval: 30000, // Update every 30 seconds
        distanceInterval: 100, // Update every 100 meters
      },
      (location) => {
        dispatch(updateCurrentLocation({
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
          accuracy: location.coords.accuracy,
          altitude: location.coords.altitude,
          heading: location.coords.heading,
          speed: location.coords.speed,
        }));
      }
    );
    return subscription;
  }
);

const locationSlice = createSlice({
  name: 'location',
  initialState,
  reducers: {
    updateCurrentLocation: (state, action: PayloadAction<LocationCoordinates>) => {
      state.currentLocation = action.payload;
      state.lastUpdated = new Date().toISOString();
    },
    setSearchRadius: (state, action: PayloadAction<number>) => {
      state.searchRadius = action.payload;
    },
    setNearbyProfessionals: (state, action: PayloadAction<any[]>) => {
      state.nearbyProfessionals = action.payload;
    },
    clearLocationError: (state) => {
      state.error = null;
    },
    stopTracking: (state) => {
      state.isTracking = false;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(requestLocationPermission.fulfilled, (state, action) => {
        state.permissionStatus = action.payload === 'granted' ? 'granted' : 'denied';
      })
      .addCase(getCurrentLocation.pending, (state) => {
        state.error = null;
      })
      .addCase(getCurrentLocation.fulfilled, (state, action) => {
        state.currentLocation = action.payload;
        state.lastUpdated = new Date().toISOString();
        state.error = null;
      })
      .addCase(getCurrentLocation.rejected, (state, action) => {
        state.error = action.payload as string;
      })
      .addCase(startLocationTracking.fulfilled, (state) => {
        state.isTracking = true;
      });
  },
});

export const {
  updateCurrentLocation,
  setSearchRadius,
  setNearbyProfessionals,
  clearLocationError,
  stopTracking,
} = locationSlice.actions;

export default locationSlice.reducer;
