import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

export interface MarketplaceItem {
  id: string;
  title: string;
  description: string;
  category: 'tools' | 'equipment' | 'materials' | 'components';
  subcategory: string;
  images: string[];
  condition: 'new' | 'like_new' | 'good' | 'fair' | 'poor';
  availability: 'available' | 'rented' | 'sold' | 'reserved';
  owner: {
    id: string;
    name: string;
    rating: number;
    location: string;
    distance?: number;
  };
  pricing: {
    type: 'sale' | 'rent' | 'exchange' | 'free' | 'time_bank';
    amount?: number;
    currency?: string;
    timeValue?: number; // for time banking
    exchangeFor?: string; // for exchanges
    rentalPeriod?: 'hour' | 'day' | 'week' | 'month';
  };
  location: {
    latitude: number;
    longitude: number;
    address: string;
    city: string;
    region: string;
  };
  specifications?: {
    brand?: string;
    model?: string;
    voltage?: string;
    power?: string;
    [key: string]: any;
  };
  safetyInfo?: {
    antiCounterfeit: boolean;
    certifications: string[];
    warnings: string[];
  };
  createdAt: string;
  updatedAt: string;
}

export interface Transaction {
  id: string;
  itemId: string;
  buyerId: string;
  sellerId: string;
  type: 'sale' | 'rental' | 'exchange' | 'time_bank';
  status: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'disputed';
  amount?: number;
  currency?: string;
  timeValue?: number;
  startDate?: string;
  endDate?: string;
  deliveryMethod: 'pickup' | 'delivery' | 'meetup';
  paymentMethod: 'cash' | 'mobile_money' | 'bank_transfer' | 'time_bank' | 'exchange';
  createdAt: string;
  updatedAt: string;
}

export interface MarketplaceState {
  items: MarketplaceItem[];
  myItems: MarketplaceItem[];
  favoriteItems: string[];
  transactions: Transaction[];
  searchQuery: string;
  filters: {
    category: string[];
    condition: string[];
    priceRange: { min: number; max: number };
    distance: number;
    availability: string[];
    paymentMethods: string[];
  };
  sortBy: 'newest' | 'price_low' | 'price_high' | 'distance' | 'rating';
  isLoading: boolean;
  error: string | null;
}

const initialState: MarketplaceState = {
  items: [],
  myItems: [],
  favoriteItems: [],
  transactions: [],
  searchQuery: '',
  filters: {
    category: [],
    condition: [],
    priceRange: { min: 0, max: 1000000 },
    distance: 50,
    availability: ['available'],
    paymentMethods: [],
  },
  sortBy: 'newest',
  isLoading: false,
  error: null,
};

// Async thunks
export const fetchMarketplaceItems = createAsyncThunk(
  'marketplace/fetchItems',
  async (params: {
    query?: string;
    category?: string;
    location?: { latitude: number; longitude: number };
    radius?: number;
  }) => {
    // TODO: Implement actual API call
    const queryParams = new URLSearchParams();
    if (params.query) queryParams.append('q', params.query);
    if (params.category) queryParams.append('category', params.category);
    if (params.location) {
      queryParams.append('lat', params.location.latitude.toString());
      queryParams.append('lng', params.location.longitude.toString());
    }
    if (params.radius) queryParams.append('radius', params.radius.toString());

    const response = await fetch(`/api/marketplace/items?${queryParams}`);
    return response.json();
  }
);

export const createMarketplaceItem = createAsyncThunk(
  'marketplace/createItem',
  async (item: Omit<MarketplaceItem, 'id' | 'createdAt' | 'updatedAt'>) => {
    // TODO: Implement actual API call
    const response = await fetch('/api/marketplace/items', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(item),
    });
    return response.json();
  }
);

export const createTransaction = createAsyncThunk(
  'marketplace/createTransaction',
  async (transaction: Omit<Transaction, 'id' | 'createdAt' | 'updatedAt'>) => {
    // TODO: Implement actual API call
    const response = await fetch('/api/marketplace/transactions', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(transaction),
    });
    return response.json();
  }
);

const marketplaceSlice = createSlice({
  name: 'marketplace',
  initialState,
  reducers: {
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },
    updateFilters: (state, action: PayloadAction<Partial<typeof initialState.filters>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    setSortBy: (state, action: PayloadAction<typeof initialState.sortBy>) => {
      state.sortBy = action.payload;
    },
    toggleFavorite: (state, action: PayloadAction<string>) => {
      const itemId = action.payload;
      const index = state.favoriteItems.indexOf(itemId);
      if (index === -1) {
        state.favoriteItems.push(itemId);
      } else {
        state.favoriteItems.splice(index, 1);
      }
    },
    updateTransaction: (state, action: PayloadAction<{ id: string; updates: Partial<Transaction> }>) => {
      const index = state.transactions.findIndex(t => t.id === action.payload.id);
      if (index !== -1) {
        state.transactions[index] = { ...state.transactions[index], ...action.payload.updates };
      }
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchMarketplaceItems.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchMarketplaceItems.fulfilled, (state, action) => {
        state.isLoading = false;
        state.items = action.payload;
      })
      .addCase(fetchMarketplaceItems.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch items';
      })
      .addCase(createMarketplaceItem.fulfilled, (state, action) => {
        state.myItems.push(action.payload);
      })
      .addCase(createTransaction.fulfilled, (state, action) => {
        state.transactions.push(action.payload);
      });
  },
});

export const {
  setSearchQuery,
  updateFilters,
  setSortBy,
  toggleFavorite,
  updateTransaction,
  clearError,
} = marketplaceSlice.actions;

export default marketplaceSlice.reducer;
