import React, { createContext, useContext, useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../store/store';
import { updatePreferences } from '../store/slices/userSlice';

type Language = 'fr' | 'wo' | 'ar' | 'en';

interface Translations {
  [key: string]: {
    [lang in Language]: string;
  };
}

// Core translations for the app
const translations: Translations = {
  // Navigation
  'nav.home': {
    fr: 'Accueil',
    wo: 'Kër',
    ar: 'الرئيسية',
    en: 'Home',
  },
  'nav.professionals': {
    fr: 'Électriciens',
    wo: 'Mbëj-elektrik',
    ar: 'الكهربائيون',
    en: 'Electricians',
  },
  'nav.marketplace': {
    fr: 'Marché',
    wo: 'Marché',
    ar: 'السوق',
    en: 'Marketplace',
  },
  'nav.chat': {
    fr: 'Messages',
    wo: 'Bataaxal',
    ar: 'الرسائل',
    en: 'Messages',
  },
  'nav.profile': {
    fr: 'Profil',
    wo: 'Profil',
    ar: 'الملف الشخصي',
    en: 'Profile',
  },

  // Common actions
  'action.search': {
    fr: 'Rechercher',
    wo: 'Ceet',
    ar: 'بحث',
    en: 'Search',
  },
  'action.save': {
    fr: 'Enregistrer',
    wo: 'Defar',
    ar: 'حفظ',
    en: 'Save',
  },
  'action.cancel': {
    fr: 'Annuler',
    wo: 'Bàyyi',
    ar: 'إلغاء',
    en: 'Cancel',
  },
  'action.confirm': {
    fr: 'Confirmer',
    wo: 'Dëgg',
    ar: 'تأكيد',
    en: 'Confirm',
  },
  'action.emergency': {
    fr: 'Urgence',
    wo: 'Urgence',
    ar: 'طوارئ',
    en: 'Emergency',
  },

  // Home screen
  'home.welcome': {
    fr: 'Bienvenue sur ProMatos',
    wo: 'Dalal ak jamm ci ProMatos',
    ar: 'مرحباً بك في ProMatos',
    en: 'Welcome to ProMatos',
  },
  'home.subtitle': {
    fr: 'Votre assistant électrique intelligent',
    wo: 'Sa mbëj-elektrik bu xam-xam',
    ar: 'مساعدك الكهربائي الذكي',
    en: 'Your intelligent electrical assistant',
  },
  'home.search_placeholder': {
    fr: 'Que recherchez-vous ? (ex: "J\'ai besoin d\'une perceuse à Dakar")',
    wo: 'Lan nga bëgg a ceet? (misaal: "Bëgg naa benn perceuse ci Dakar")',
    ar: 'ماذا تبحث عن؟ (مثال: "أحتاج مثقاب في داكار")',
    en: 'What are you looking for? (e.g., "I need a drill in Dakar")',
  },

  // Emergency
  'emergency.title': {
    fr: 'Urgence Électrique',
    wo: 'Urgence Elektrik',
    ar: 'طوارئ كهربائية',
    en: 'Electrical Emergency',
  },
  'emergency.description': {
    fr: 'Décrivez votre situation d\'urgence',
    wo: 'Wax ci sa xaalis bu urgence',
    ar: 'صف حالة الطوارئ الخاصة بك',
    en: 'Describe your emergency situation',
  },

  // Safety
  'safety.counterfeit_warning': {
    fr: 'Attention: Produit potentiellement contrefait détecté',
    wo: 'Tànk: Produit bu mën a nekk contrefait',
    ar: 'تحذير: تم اكتشاف منتج مقلد محتمل',
    en: 'Warning: Potentially counterfeit product detected',
  },
  'safety.electrical_hazard': {
    fr: 'Danger électrique détecté',
    wo: 'Jafe elektrik yi nekk',
    ar: 'تم اكتشاف خطر كهربائي',
    en: 'Electrical hazard detected',
  },

  // Marketplace
  'marketplace.tools': {
    fr: 'Outils',
    wo: 'Jumtukaay',
    ar: 'أدوات',
    en: 'Tools',
  },
  'marketplace.equipment': {
    fr: 'Équipements',
    wo: 'Matériel',
    ar: 'معدات',
    en: 'Equipment',
  },
  'marketplace.materials': {
    fr: 'Matériaux',
    wo: 'Matériau',
    ar: 'مواد',
    en: 'Materials',
  },

  // Professional features
  'professional.available': {
    fr: 'Disponible',
    wo: 'Am na',
    ar: 'متاح',
    en: 'Available',
  },
  'professional.busy': {
    fr: 'Occupé',
    wo: 'Dafa am liggéey',
    ar: 'مشغول',
    en: 'Busy',
  },
  'professional.offline': {
    fr: 'Hors ligne',
    wo: 'Amul ci internet',
    ar: 'غير متصل',
    en: 'Offline',
  },
};

interface LanguageContextType {
  currentLanguage: Language;
  setLanguage: (language: Language) => void;
  t: (key: string, fallback?: string) => string;
  isRTL: boolean;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const dispatch = useDispatch();
  const userLanguage = useSelector((state: RootState) => state.user.preferences.language);
  const [currentLanguage, setCurrentLanguage] = useState<Language>(userLanguage || 'fr');

  const isRTL = currentLanguage === 'ar';

  useEffect(() => {
    if (userLanguage && userLanguage !== currentLanguage) {
      setCurrentLanguage(userLanguage);
    }
  }, [userLanguage]);

  const setLanguage = (language: Language) => {
    setCurrentLanguage(language);
    dispatch(updatePreferences({ language }));
  };

  const t = (key: string, fallback?: string): string => {
    const translation = translations[key];
    if (translation && translation[currentLanguage]) {
      return translation[currentLanguage];
    }
    
    // Fallback to French if current language not available
    if (translation && translation.fr) {
      return translation.fr;
    }
    
    // Return fallback or key if no translation found
    return fallback || key;
  };

  const contextValue: LanguageContextType = {
    currentLanguage,
    setLanguage,
    t,
    isRTL,
  };

  return (
    <LanguageContext.Provider value={contextValue}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage(): LanguageContextType {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}

// Helper hook for translations
export function useTranslation() {
  const { t } = useLanguage();
  return { t };
}
