import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface OfflineAction {
  id: string;
  type: string;
  payload: any;
  timestamp: string;
  retryCount: number;
  maxRetries: number;
}

export interface CachedData {
  key: string;
  data: any;
  timestamp: string;
  expiresAt: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
}

export interface SyncStatus {
  isOnline: boolean;
  lastSyncAt: string | null;
  pendingActions: number;
  failedActions: number;
  syncInProgress: boolean;
}

export interface OfflineState {
  isOnline: boolean;
  pendingActions: OfflineAction[];
  failedActions: OfflineAction[];
  cachedData: CachedData[];
  syncStatus: SyncStatus;
  offlineMode: 'auto' | 'forced' | 'disabled';
  meshNetwork: {
    enabled: boolean;
    connectedPeers: string[];
    sharedData: { [key: string]: any };
  };
  settings: {
    maxCacheSize: number; // in MB
    maxRetries: number;
    retryInterval: number; // in seconds
    enableMeshNetwork: boolean;
    prioritizeEmergencyData: boolean;
  };
}

const initialState: OfflineState = {
  isOnline: true,
  pendingActions: [],
  failedActions: [],
  cachedData: [],
  syncStatus: {
    isOnline: true,
    lastSyncAt: null,
    pendingActions: 0,
    failedActions: 0,
    syncInProgress: false,
  },
  offlineMode: 'auto',
  meshNetwork: {
    enabled: false,
    connectedPeers: [],
    sharedData: {},
  },
  settings: {
    maxCacheSize: 100, // 100MB
    maxRetries: 3,
    retryInterval: 30, // 30 seconds
    enableMeshNetwork: true,
    prioritizeEmergencyData: true,
  },
};

const offlineSlice = createSlice({
  name: 'offline',
  initialState,
  reducers: {
    setOnlineStatus: (state, action: PayloadAction<boolean>) => {
      state.isOnline = action.payload;
      state.syncStatus.isOnline = action.payload;
      
      if (action.payload && state.pendingActions.length > 0) {
        // Trigger sync when coming back online
        state.syncStatus.syncInProgress = true;
      }
    },
    
    addPendingAction: (state, action: PayloadAction<Omit<OfflineAction, 'id' | 'timestamp' | 'retryCount'>>) => {
      const pendingAction: OfflineAction = {
        ...action.payload,
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        timestamp: new Date().toISOString(),
        retryCount: 0,
      };
      state.pendingActions.push(pendingAction);
      state.syncStatus.pendingActions = state.pendingActions.length;
    },
    
    removePendingAction: (state, action: PayloadAction<string>) => {
      state.pendingActions = state.pendingActions.filter(action => action.id !== action.payload);
      state.syncStatus.pendingActions = state.pendingActions.length;
    },
    
    moveToFailedActions: (state, action: PayloadAction<string>) => {
      const actionIndex = state.pendingActions.findIndex(a => a.id === action.payload);
      if (actionIndex !== -1) {
        const failedAction = state.pendingActions[actionIndex];
        failedAction.retryCount++;
        
        if (failedAction.retryCount >= failedAction.maxRetries) {
          state.failedActions.push(failedAction);
          state.pendingActions.splice(actionIndex, 1);
        }
      }
      state.syncStatus.pendingActions = state.pendingActions.length;
      state.syncStatus.failedActions = state.failedActions.length;
    },
    
    retryFailedAction: (state, action: PayloadAction<string>) => {
      const actionIndex = state.failedActions.findIndex(a => a.id === action.payload);
      if (actionIndex !== -1) {
        const retryAction = state.failedActions[actionIndex];
        retryAction.retryCount = 0;
        state.pendingActions.push(retryAction);
        state.failedActions.splice(actionIndex, 1);
      }
      state.syncStatus.pendingActions = state.pendingActions.length;
      state.syncStatus.failedActions = state.failedActions.length;
    },
    
    addCachedData: (state, action: PayloadAction<Omit<CachedData, 'timestamp'>>) => {
      const cachedItem: CachedData = {
        ...action.payload,
        timestamp: new Date().toISOString(),
      };
      
      // Remove existing cache with same key
      state.cachedData = state.cachedData.filter(item => item.key !== cachedItem.key);
      
      // Add new cache item
      state.cachedData.push(cachedItem);
      
      // Sort by priority and timestamp
      state.cachedData.sort((a, b) => {
        const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
        if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
          return priorityOrder[b.priority] - priorityOrder[a.priority];
        }
        return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
      });
      
      // Implement cache size management
      // TODO: Implement actual size calculation and cleanup
    },
    
    removeCachedData: (state, action: PayloadAction<string>) => {
      state.cachedData = state.cachedData.filter(item => item.key !== action.payload);
    },
    
    clearExpiredCache: (state) => {
      const now = new Date().toISOString();
      state.cachedData = state.cachedData.filter(item => item.expiresAt > now);
    },
    
    setSyncInProgress: (state, action: PayloadAction<boolean>) => {
      state.syncStatus.syncInProgress = action.payload;
      if (!action.payload) {
        state.syncStatus.lastSyncAt = new Date().toISOString();
      }
    },
    
    setOfflineMode: (state, action: PayloadAction<'auto' | 'forced' | 'disabled'>) => {
      state.offlineMode = action.payload;
    },
    
    updateMeshNetwork: (state, action: PayloadAction<Partial<typeof initialState.meshNetwork>>) => {
      state.meshNetwork = { ...state.meshNetwork, ...action.payload };
    },
    
    addMeshPeer: (state, action: PayloadAction<string>) => {
      if (!state.meshNetwork.connectedPeers.includes(action.payload)) {
        state.meshNetwork.connectedPeers.push(action.payload);
      }
    },
    
    removeMeshPeer: (state, action: PayloadAction<string>) => {
      state.meshNetwork.connectedPeers = state.meshNetwork.connectedPeers.filter(
        peer => peer !== action.payload
      );
    },
    
    updateMeshSharedData: (state, action: PayloadAction<{ key: string; data: any }>) => {
      state.meshNetwork.sharedData[action.payload.key] = action.payload.data;
    },
    
    updateOfflineSettings: (state, action: PayloadAction<Partial<typeof initialState.settings>>) => {
      state.settings = { ...state.settings, ...action.payload };
    },
    
    clearAllPendingActions: (state) => {
      state.pendingActions = [];
      state.syncStatus.pendingActions = 0;
    },
    
    clearAllFailedActions: (state) => {
      state.failedActions = [];
      state.syncStatus.failedActions = 0;
    },
  },
});

export const {
  setOnlineStatus,
  addPendingAction,
  removePendingAction,
  moveToFailedActions,
  retryFailedAction,
  addCachedData,
  removeCachedData,
  clearExpiredCache,
  setSyncInProgress,
  setOfflineMode,
  updateMeshNetwork,
  addMeshPeer,
  removeMeshPeer,
  updateMeshSharedData,
  updateOfflineSettings,
  clearAllPendingActions,
  clearAllFailedActions,
} = offlineSlice.actions;

export default offlineSlice.reducer;
