import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useDispatch } from 'react-redux';

import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from '../../contexts/LanguageContext';
import { setUser } from '../../store/slices/authSlice';

export default function RegisterScreen({ navigation }: any) {
  const { theme } = useTheme();
  const { t } = useTranslation();
  const dispatch = useDispatch();
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    userType: 'individual' as 'individual' | 'professional' | 'supplier',
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleRegister = async () => {
    if (!formData.name || !formData.email || !formData.password) {
      Alert.alert('Erreur', 'Veuillez remplir tous les champs obligatoires');
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      Alert.alert('Erreur', 'Les mots de passe ne correspondent pas');
      return;
    }

    setIsLoading(true);
    
    // Simulate registration for demo purposes
    setTimeout(() => {
      dispatch(setUser({
        id: '1',
        email: formData.email,
        name: formData.name,
        phone: formData.phone,
        userType: formData.userType,
        isVerified: false,
        createdAt: new Date().toISOString(),
      }));
      setIsLoading(false);
    }, 1000);
  };

  const userTypes = [
    { value: 'individual', label: 'Particulier', icon: 'person-outline' },
    { value: 'professional', label: 'Électricien', icon: 'construct-outline' },
    { value: 'supplier', label: 'Fournisseur', icon: 'storefront-outline' },
  ];

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollContainer: {
      padding: theme.spacing.lg,
    },
    header: {
      alignItems: 'center',
      marginBottom: theme.spacing.xl,
    },
    title: {
      ...theme.typography.h2,
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
    },
    subtitle: {
      ...theme.typography.body,
      color: theme.colors.textSecondary,
      textAlign: 'center',
    },
    inputContainer: {
      marginBottom: theme.spacing.lg,
    },
    label: {
      ...theme.typography.body,
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
      fontWeight: '600',
    },
    input: {
      ...theme.typography.body,
      color: theme.colors.text,
      backgroundColor: theme.colors.surface,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    userTypeContainer: {
      marginBottom: theme.spacing.lg,
    },
    userTypeOptions: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    userTypeOption: {
      flex: 1,
      backgroundColor: theme.colors.surface,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      alignItems: 'center',
      marginHorizontal: theme.spacing.xs,
      borderWidth: 2,
      borderColor: 'transparent',
    },
    userTypeOptionSelected: {
      borderColor: theme.colors.primary,
      backgroundColor: theme.colors.primary + '20',
    },
    userTypeIcon: {
      marginBottom: theme.spacing.sm,
    },
    userTypeText: {
      ...theme.typography.caption,
      color: theme.colors.text,
      textAlign: 'center',
      fontWeight: '600',
    },
    registerButton: {
      backgroundColor: theme.colors.primary,
      borderRadius: theme.borderRadius.lg,
      padding: theme.spacing.lg,
      alignItems: 'center',
      marginTop: theme.spacing.lg,
    },
    registerButtonText: {
      ...theme.typography.body,
      color: 'white',
      fontWeight: '600',
    },
    loginContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: theme.spacing.lg,
    },
    loginText: {
      ...theme.typography.body,
      color: theme.colors.textSecondary,
    },
    loginLink: {
      ...theme.typography.body,
      color: theme.colors.primary,
      fontWeight: '600',
      marginLeft: theme.spacing.sm,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollContainer}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Créer un compte</Text>
          <Text style={styles.subtitle}>Rejoignez la communauté ProMatos</Text>
        </View>

        {/* Form */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Nom complet *</Text>
          <TextInput
            style={styles.input}
            placeholder="Votre nom complet"
            placeholderTextColor={theme.colors.textSecondary}
            value={formData.name}
            onChangeText={(text) => setFormData({ ...formData, name: text })}
          />
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>Email *</Text>
          <TextInput
            style={styles.input}
            placeholder="<EMAIL>"
            placeholderTextColor={theme.colors.textSecondary}
            value={formData.email}
            onChangeText={(text) => setFormData({ ...formData, email: text })}
            keyboardType="email-address"
            autoCapitalize="none"
          />
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>Téléphone</Text>
          <TextInput
            style={styles.input}
            placeholder="+221 XX XXX XX XX"
            placeholderTextColor={theme.colors.textSecondary}
            value={formData.phone}
            onChangeText={(text) => setFormData({ ...formData, phone: text })}
            keyboardType="phone-pad"
          />
        </View>

        <View style={styles.userTypeContainer}>
          <Text style={styles.label}>Type de compte *</Text>
          <View style={styles.userTypeOptions}>
            {userTypes.map((type) => (
              <TouchableOpacity
                key={type.value}
                style={[
                  styles.userTypeOption,
                  formData.userType === type.value && styles.userTypeOptionSelected,
                ]}
                onPress={() => setFormData({ ...formData, userType: type.value as any })}
              >
                <View style={styles.userTypeIcon}>
                  <Ionicons 
                    name={type.icon as any} 
                    size={24} 
                    color={formData.userType === type.value ? theme.colors.primary : theme.colors.textSecondary} 
                  />
                </View>
                <Text style={styles.userTypeText}>{type.label}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>Mot de passe *</Text>
          <TextInput
            style={styles.input}
            placeholder="Votre mot de passe"
            placeholderTextColor={theme.colors.textSecondary}
            value={formData.password}
            onChangeText={(text) => setFormData({ ...formData, password: text })}
            secureTextEntry
          />
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>Confirmer le mot de passe *</Text>
          <TextInput
            style={styles.input}
            placeholder="Confirmez votre mot de passe"
            placeholderTextColor={theme.colors.textSecondary}
            value={formData.confirmPassword}
            onChangeText={(text) => setFormData({ ...formData, confirmPassword: text })}
            secureTextEntry
          />
        </View>

        <TouchableOpacity 
          style={styles.registerButton} 
          onPress={handleRegister}
          disabled={isLoading}
        >
          <Text style={styles.registerButtonText}>
            {isLoading ? 'Création...' : 'Créer le compte'}
          </Text>
        </TouchableOpacity>

        <View style={styles.loginContainer}>
          <Text style={styles.loginText}>Déjà un compte ?</Text>
          <TouchableOpacity onPress={() => navigation.navigate('Login')}>
            <Text style={styles.loginLink}>Se connecter</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
