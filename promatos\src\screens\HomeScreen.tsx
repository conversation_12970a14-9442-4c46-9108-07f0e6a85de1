import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useDispatch, useSelector } from 'react-redux';
import { SafeAreaView } from 'react-native-safe-area-context';

import { useTheme } from '../contexts/ThemeContext';
import { useTranslation } from '../contexts/LanguageContext';
import { RootState } from '../store/store';
import { processNaturalLanguageRequest } from '../store/slices/contextualSlice';
import { getCurrentLocation } from '../store/slices/locationSlice';

export default function HomeScreen({ navigation }: any) {
  const { theme } = useTheme();
  const { t } = useTranslation();
  const dispatch = useDispatch();
  
  const [searchQuery, setSearchQuery] = useState('');
  const { currentLocation } = useSelector((state: RootState) => state.location);
  const { user } = useSelector((state: RootState) => state.auth);
  const { isProcessing, currentRequest } = useSelector((state: RootState) => state.contextual);

  useEffect(() => {
    // Get user location on app start
    dispatch(getCurrentLocation() as any);
  }, [dispatch]);

  const handleSearch = async () => {
    if (!searchQuery.trim()) return;

    try {
      await dispatch(processNaturalLanguageRequest({
        text: searchQuery,
        context: {
          userLocation: currentLocation || undefined,
          userProfile: user,
          previousRequests: [],
        },
      }) as any);
      
      // Navigate based on the processed request intent
      if (currentRequest) {
        switch (currentRequest.intent) {
          case 'find_professional':
            navigation.navigate('Professionals');
            break;
          case 'find_tool':
          case 'buy_sell':
            navigation.navigate('Marketplace');
            break;
          case 'emergency':
            navigation.navigate('Emergency');
            break;
          default:
            // Stay on home screen and show results
            break;
        }
      }
    } catch (error) {
      Alert.alert('Erreur', 'Impossible de traiter votre demande. Veuillez réessayer.');
    }
  };

  const handleEmergency = () => {
    navigation.navigate('Emergency');
  };

  const quickActions = [
    {
      id: 'find_electrician',
      title: 'Trouver un électricien',
      icon: 'person-outline',
      color: theme.colors.primary,
      onPress: () => navigation.navigate('Professionals'),
    },
    {
      id: 'rent_tools',
      title: 'Louer des outils',
      icon: 'construct-outline',
      color: theme.colors.secondary,
      onPress: () => navigation.navigate('Marketplace'),
    },
    {
      id: 'safety_check',
      title: 'Vérification sécurité',
      icon: 'shield-checkmark-outline',
      color: theme.colors.success,
      onPress: () => {
        // TODO: Navigate to safety check feature
      },
    },
    {
      id: 'emergency',
      title: 'Urgence',
      icon: 'warning-outline',
      color: theme.colors.emergency,
      onPress: handleEmergency,
    },
  ];

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      padding: theme.spacing.lg,
      backgroundColor: theme.colors.primary,
    },
    welcomeText: {
      ...theme.typography.h2,
      color: 'white',
      marginBottom: theme.spacing.sm,
    },
    subtitleText: {
      ...theme.typography.body,
      color: 'white',
      opacity: 0.9,
    },
    searchContainer: {
      margin: theme.spacing.lg,
      backgroundColor: theme.colors.surface,
      borderRadius: theme.borderRadius.lg,
      padding: theme.spacing.md,
      elevation: 2,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
    },
    searchInput: {
      ...theme.typography.body,
      color: theme.colors.text,
      minHeight: 80,
      textAlignVertical: 'top',
      marginBottom: theme.spacing.md,
    },
    searchButton: {
      backgroundColor: theme.colors.primary,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    searchButtonText: {
      ...theme.typography.body,
      color: 'white',
      fontWeight: '600',
      marginLeft: theme.spacing.sm,
    },
    quickActionsContainer: {
      padding: theme.spacing.lg,
    },
    quickActionsTitle: {
      ...theme.typography.h3,
      color: theme.colors.text,
      marginBottom: theme.spacing.md,
    },
    quickActionsGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
    },
    quickActionItem: {
      width: '48%',
      backgroundColor: theme.colors.surface,
      borderRadius: theme.borderRadius.lg,
      padding: theme.spacing.lg,
      alignItems: 'center',
      marginBottom: theme.spacing.md,
      elevation: 1,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
    },
    quickActionIcon: {
      marginBottom: theme.spacing.sm,
    },
    quickActionText: {
      ...theme.typography.caption,
      color: theme.colors.text,
      textAlign: 'center',
      fontWeight: '600',
    },
    emergencyButton: {
      position: 'absolute',
      bottom: theme.spacing.xl,
      right: theme.spacing.lg,
      backgroundColor: theme.colors.emergency,
      borderRadius: 30,
      width: 60,
      height: 60,
      alignItems: 'center',
      justifyContent: 'center',
      elevation: 4,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.3,
      shadowRadius: 4,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.welcomeText}>
            {t('home.welcome')}
          </Text>
          <Text style={styles.subtitleText}>
            {t('home.subtitle')}
          </Text>
        </View>

        {/* Search Container */}
        <View style={styles.searchContainer}>
          <TextInput
            style={styles.searchInput}
            placeholder={t('home.search_placeholder')}
            placeholderTextColor={theme.colors.textSecondary}
            value={searchQuery}
            onChangeText={setSearchQuery}
            multiline
          />
          <TouchableOpacity 
            style={styles.searchButton} 
            onPress={handleSearch}
            disabled={isProcessing}
          >
            <Ionicons 
              name={isProcessing ? "hourglass-outline" : "search-outline"} 
              size={20} 
              color="white" 
            />
            <Text style={styles.searchButtonText}>
              {isProcessing ? 'Traitement...' : t('action.search')}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActionsContainer}>
          <Text style={styles.quickActionsTitle}>Actions rapides</Text>
          <View style={styles.quickActionsGrid}>
            {quickActions.map((action) => (
              <TouchableOpacity
                key={action.id}
                style={styles.quickActionItem}
                onPress={action.onPress}
              >
                <View style={styles.quickActionIcon}>
                  <Ionicons 
                    name={action.icon as any} 
                    size={32} 
                    color={action.color} 
                  />
                </View>
                <Text style={styles.quickActionText}>{action.title}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>

      {/* Emergency FAB */}
      <TouchableOpacity style={styles.emergencyButton} onPress={handleEmergency}>
        <Ionicons name="warning" size={24} color="white" />
      </TouchableOpacity>
    </SafeAreaView>
  );
}
