import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface UserPreferences {
  language: 'fr' | 'wo' | 'ar' | 'en';
  theme: 'light' | 'dark' | 'auto';
  notifications: {
    push: boolean;
    email: boolean;
    sms: boolean;
    emergencyAlerts: boolean;
  };
  accessibility: {
    voiceInterface: boolean;
    largeText: boolean;
    highContrast: boolean;
    simplifiedUI: boolean;
  };
  location: {
    shareLocation: boolean;
    preciseLocation: boolean;
    backgroundLocation: boolean;
  };
}

export interface UserProfile {
  bio?: string;
  skills?: string[];
  certifications?: string[];
  experience?: number;
  rating?: number;
  reviewCount?: number;
  location?: {
    city: string;
    region: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  availability?: {
    status: 'available' | 'busy' | 'offline';
    schedule?: {
      [key: string]: { start: string; end: string }[];
    };
  };
}

export interface UserState {
  profile: UserProfile;
  preferences: UserPreferences;
  isProfileComplete: boolean;
  lastActiveAt: string | null;
}

const initialState: UserState = {
  profile: {},
  preferences: {
    language: 'fr',
    theme: 'auto',
    notifications: {
      push: true,
      email: true,
      sms: false,
      emergencyAlerts: true,
    },
    accessibility: {
      voiceInterface: false,
      largeText: false,
      highContrast: false,
      simplifiedUI: false,
    },
    location: {
      shareLocation: true,
      preciseLocation: false,
      backgroundLocation: false,
    },
  },
  isProfileComplete: false,
  lastActiveAt: null,
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    updateProfile: (state, action: PayloadAction<Partial<UserProfile>>) => {
      state.profile = { ...state.profile, ...action.payload };
      state.isProfileComplete = checkProfileComplete(state.profile);
    },
    updatePreferences: (state, action: PayloadAction<Partial<UserPreferences>>) => {
      state.preferences = { ...state.preferences, ...action.payload };
    },
    setLastActiveAt: (state, action: PayloadAction<string>) => {
      state.lastActiveAt = action.payload;
    },
    resetUserData: (state) => {
      return initialState;
    },
  },
});

// Helper function to check if profile is complete
function checkProfileComplete(profile: UserProfile): boolean {
  return !!(
    profile.bio &&
    profile.location?.city &&
    profile.location?.region
  );
}

export const {
  updateProfile,
  updatePreferences,
  setLastActiveAt,
  resetUserData,
} = userSlice.actions;

export default userSlice.reducer;
