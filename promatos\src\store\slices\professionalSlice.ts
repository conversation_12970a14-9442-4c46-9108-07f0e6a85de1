import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

export interface Professional {
  id: string;
  name: string;
  email: string;
  phone: string;
  profilePicture?: string;
  specialties: string[];
  certifications: string[];
  experience: number;
  rating: number;
  reviewCount: number;
  location: {
    latitude: number;
    longitude: number;
    address: string;
    city: string;
    region: string;
  };
  availability: {
    status: 'available' | 'busy' | 'offline';
    emergencyAvailable: boolean;
    responseTime: number; // in minutes
  };
  pricing: {
    hourlyRate?: number;
    emergencyRate?: number;
    currency: string;
  };
  verificationStatus: {
    identity: boolean;
    certifications: boolean;
    insurance: boolean;
  };
  lastActive: string;
  distance?: number; // calculated distance from user
}

export interface EmergencyAlert {
  id: string;
  type: 'electrical_hazard' | 'power_outage' | 'equipment_failure' | 'safety_concern';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  location: {
    latitude: number;
    longitude: number;
    address: string;
  };
  reportedBy: string;
  timestamp: string;
  status: 'active' | 'acknowledged' | 'resolved';
  assignedProfessionals: string[];
}

export interface ProfessionalState {
  professionals: Professional[];
  nearbyProfessionals: Professional[];
  selectedProfessional: Professional | null;
  emergencyAlerts: EmergencyAlert[];
  activeEmergency: EmergencyAlert | null;
  searchFilters: {
    specialties: string[];
    maxDistance: number;
    minRating: number;
    availableNow: boolean;
    emergencyOnly: boolean;
  };
  isLoading: boolean;
  error: string | null;
}

const initialState: ProfessionalState = {
  professionals: [],
  nearbyProfessionals: [],
  selectedProfessional: null,
  emergencyAlerts: [],
  activeEmergency: null,
  searchFilters: {
    specialties: [],
    maxDistance: 10,
    minRating: 0,
    availableNow: false,
    emergencyOnly: false,
  },
  isLoading: false,
  error: null,
};

// Async thunks
export const fetchNearbyProfessionals = createAsyncThunk(
  'professional/fetchNearby',
  async (params: { latitude: number; longitude: number; radius: number }) => {
    // TODO: Implement actual API call
    const response = await fetch(`/api/professionals/nearby?lat=${params.latitude}&lng=${params.longitude}&radius=${params.radius}`);
    return response.json();
  }
);

export const sendEmergencyAlert = createAsyncThunk(
  'professional/sendEmergencyAlert',
  async (alert: Omit<EmergencyAlert, 'id' | 'timestamp' | 'status' | 'assignedProfessionals'>) => {
    // TODO: Implement actual API call
    const response = await fetch('/api/emergency/alert', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(alert),
    });
    return response.json();
  }
);

export const requestProfessionalService = createAsyncThunk(
  'professional/requestService',
  async (request: {
    professionalId: string;
    serviceType: string;
    description: string;
    urgency: 'low' | 'medium' | 'high' | 'emergency';
    location: { latitude: number; longitude: number; address: string };
  }) => {
    // TODO: Implement actual API call
    const response = await fetch('/api/services/request', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(request),
    });
    return response.json();
  }
);

const professionalSlice = createSlice({
  name: 'professional',
  initialState,
  reducers: {
    setSelectedProfessional: (state, action: PayloadAction<Professional | null>) => {
      state.selectedProfessional = action.payload;
    },
    updateSearchFilters: (state, action: PayloadAction<Partial<typeof initialState.searchFilters>>) => {
      state.searchFilters = { ...state.searchFilters, ...action.payload };
    },
    addEmergencyAlert: (state, action: PayloadAction<EmergencyAlert>) => {
      state.emergencyAlerts.unshift(action.payload);
    },
    updateEmergencyAlert: (state, action: PayloadAction<{ id: string; updates: Partial<EmergencyAlert> }>) => {
      const index = state.emergencyAlerts.findIndex(alert => alert.id === action.payload.id);
      if (index !== -1) {
        state.emergencyAlerts[index] = { ...state.emergencyAlerts[index], ...action.payload.updates };
      }
    },
    setActiveEmergency: (state, action: PayloadAction<EmergencyAlert | null>) => {
      state.activeEmergency = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchNearbyProfessionals.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchNearbyProfessionals.fulfilled, (state, action) => {
        state.isLoading = false;
        state.nearbyProfessionals = action.payload;
      })
      .addCase(fetchNearbyProfessionals.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch professionals';
      })
      .addCase(sendEmergencyAlert.fulfilled, (state, action) => {
        state.emergencyAlerts.unshift(action.payload);
        state.activeEmergency = action.payload;
      })
      .addCase(requestProfessionalService.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(requestProfessionalService.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(requestProfessionalService.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to request service';
      });
  },
});

export const {
  setSelectedProfessional,
  updateSearchFilters,
  addEmergencyAlert,
  updateEmergencyAlert,
  setActiveEmergency,
  clearError,
} = professionalSlice.actions;

export default professionalSlice.reducer;
