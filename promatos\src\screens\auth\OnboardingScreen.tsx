import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

import { useTheme } from '../../contexts/ThemeContext';
import { useTranslation } from '../../contexts/LanguageContext';

const { width } = Dimensions.get('window');

interface OnboardingSlide {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  icon: string;
  color: string;
}

const onboardingSlides: OnboardingSlide[] = [
  {
    id: '1',
    title: 'Bienvenue sur ProMatos',
    subtitle: 'Votre assistant électrique intelligent',
    description: 'Connectez-vous avec des électriciens qualifiés et trouvez tous les outils dont vous avez besoin, où que vous soyez au Sénégal.',
    icon: 'flash-outline',
    color: '#2E7D32',
  },
  {
    id: '2',
    title: 'Intelligence Contextuelle',
    subtitle: 'Comprenez vos besoins en langage naturel',
    description: 'Dites simplement "J\'ai besoin d\'une perceuse à Dakar" et notre IA vous trouvera les meilleures options disponibles.',
    icon: 'chatbubble-ellipses-outline',
    color: '#1976D2',
  },
  {
    id: '3',
    title: 'Réseau de Professionnels',
    subtitle: 'Électriciens vérifiés et disponibles',
    description: 'Trouvez des électriciens certifiés près de chez vous, consultez leurs évaluations et contactez-les directement.',
    icon: 'people-outline',
    color: '#388E3C',
  },
  {
    id: '4',
    title: 'Marché Communautaire',
    subtitle: 'Partagez et échangez vos outils',
    description: 'Louez, vendez ou échangez des outils électriques avec votre communauté. Paiements flexibles acceptés.',
    icon: 'storefront-outline',
    color: '#F57C00',
  },
  {
    id: '5',
    title: 'Sécurité et Prévention',
    subtitle: 'Votre sécurité est notre priorité',
    description: 'Détection de contrefaçons, alertes de sécurité et guides de bonnes pratiques pour une utilisation sûre.',
    icon: 'shield-checkmark-outline',
    color: '#D32F2F',
  },
];

export default function OnboardingScreen({ navigation }: any) {
  const { theme } = useTheme();
  const { t } = useTranslation();
  const [currentSlide, setCurrentSlide] = useState(0);

  const nextSlide = () => {
    if (currentSlide < onboardingSlides.length - 1) {
      setCurrentSlide(currentSlide + 1);
    } else {
      navigation.navigate('Login');
    }
  };

  const skipOnboarding = () => {
    navigation.navigate('Login');
  };

  const slide = onboardingSlides[currentSlide];

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    skipButton: {
      position: 'absolute',
      top: 50,
      right: theme.spacing.lg,
      zIndex: 1,
    },
    skipText: {
      ...theme.typography.body,
      color: theme.colors.textSecondary,
    },
    slideContainer: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      paddingHorizontal: theme.spacing.xl,
    },
    iconContainer: {
      width: 120,
      height: 120,
      borderRadius: 60,
      backgroundColor: slide.color,
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: theme.spacing.xl,
    },
    title: {
      ...theme.typography.h1,
      color: theme.colors.text,
      textAlign: 'center',
      marginBottom: theme.spacing.md,
    },
    subtitle: {
      ...theme.typography.h3,
      color: slide.color,
      textAlign: 'center',
      marginBottom: theme.spacing.lg,
    },
    description: {
      ...theme.typography.body,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      lineHeight: 24,
      marginBottom: theme.spacing.xl,
    },
    bottomContainer: {
      paddingHorizontal: theme.spacing.xl,
      paddingBottom: theme.spacing.xl,
    },
    pagination: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: theme.spacing.xl,
    },
    paginationDot: {
      width: 8,
      height: 8,
      borderRadius: 4,
      marginHorizontal: 4,
    },
    activeDot: {
      backgroundColor: slide.color,
      width: 24,
    },
    inactiveDot: {
      backgroundColor: theme.colors.disabled,
    },
    nextButton: {
      backgroundColor: slide.color,
      borderRadius: theme.borderRadius.lg,
      padding: theme.spacing.lg,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    nextButtonText: {
      ...theme.typography.body,
      color: 'white',
      fontWeight: '600',
      marginRight: theme.spacing.sm,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      {/* Skip Button */}
      <TouchableOpacity style={styles.skipButton} onPress={skipOnboarding}>
        <Text style={styles.skipText}>Passer</Text>
      </TouchableOpacity>

      {/* Slide Content */}
      <View style={styles.slideContainer}>
        <View style={styles.iconContainer}>
          <Ionicons name={slide.icon as any} size={60} color="white" />
        </View>
        
        <Text style={styles.title}>{slide.title}</Text>
        <Text style={styles.subtitle}>{slide.subtitle}</Text>
        <Text style={styles.description}>{slide.description}</Text>
      </View>

      {/* Bottom Navigation */}
      <View style={styles.bottomContainer}>
        {/* Pagination */}
        <View style={styles.pagination}>
          {onboardingSlides.map((_, index) => (
            <View
              key={index}
              style={[
                styles.paginationDot,
                index === currentSlide ? styles.activeDot : styles.inactiveDot,
              ]}
            />
          ))}
        </View>

        {/* Next Button */}
        <TouchableOpacity style={styles.nextButton} onPress={nextSlide}>
          <Text style={styles.nextButtonText}>
            {currentSlide === onboardingSlides.length - 1 ? 'Commencer' : 'Suivant'}
          </Text>
          <Ionicons 
            name={currentSlide === onboardingSlides.length - 1 ? 'checkmark' : 'arrow-forward'} 
            size={20} 
            color="white" 
          />
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}
