import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

export interface ContextualRequest {
  id: string;
  originalText: string;
  processedText: string;
  intent: 'find_tool' | 'find_professional' | 'emergency' | 'learn' | 'buy_sell' | 'general';
  entities: {
    tools?: string[];
    location?: string;
    urgency?: 'low' | 'medium' | 'high' | 'emergency';
    timeframe?: string;
    budget?: number;
    specifications?: { [key: string]: any };
  };
  context: {
    userLocation?: { latitude: number; longitude: number };
    weather?: string;
    timeOfDay: string;
    userProfile: string;
    previousRequests: string[];
  };
  suggestions: {
    professionals?: string[];
    tools?: string[];
    alternatives?: string[];
    safetyTips?: string[];
  };
  confidence: number;
  timestamp: string;
}

export interface AIResponse {
  id: string;
  requestId: string;
  response: string;
  actionItems: {
    type: 'contact_professional' | 'find_tool' | 'safety_check' | 'learn_more';
    data: any;
  }[];
  followUpQuestions: string[];
  timestamp: string;
}

export interface SafetyAlert {
  id: string;
  type: 'counterfeit_warning' | 'safety_violation' | 'electrical_hazard' | 'compliance_issue';
  severity: 'info' | 'warning' | 'danger' | 'critical';
  title: string;
  description: string;
  recommendations: string[];
  relatedStandards: string[];
  location?: { latitude: number; longitude: number };
  timestamp: string;
  acknowledged: boolean;
}

export interface ContextualState {
  currentRequest: ContextualRequest | null;
  requestHistory: ContextualRequest[];
  aiResponses: AIResponse[];
  safetyAlerts: SafetyAlert[];
  isProcessing: boolean;
  error: string | null;
  nlpSettings: {
    language: 'fr' | 'wo' | 'ar' | 'en';
    voiceEnabled: boolean;
    contextAwareness: boolean;
    learningMode: boolean;
  };
}

const initialState: ContextualState = {
  currentRequest: null,
  requestHistory: [],
  aiResponses: [],
  safetyAlerts: [],
  isProcessing: false,
  error: null,
  nlpSettings: {
    language: 'fr',
    voiceEnabled: false,
    contextAwareness: true,
    learningMode: true,
  },
};

// Async thunks
export const processNaturalLanguageRequest = createAsyncThunk(
  'contextual/processRequest',
  async (params: {
    text: string;
    context: {
      userLocation?: { latitude: number; longitude: number };
      userProfile: any;
      previousRequests: string[];
    };
  }) => {
    // TODO: Implement actual AI/NLP API call
    const response = await fetch('/api/ai/process', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(params),
    });
    return response.json();
  }
);

export const detectCounterfeitProduct = createAsyncThunk(
  'contextual/detectCounterfeit',
  async (params: {
    productInfo: {
      name: string;
      brand?: string;
      model?: string;
      images?: string[];
      specifications?: any;
    };
    location?: { latitude: number; longitude: number };
  }) => {
    // TODO: Implement actual anti-counterfeit API call
    const response = await fetch('/api/ai/counterfeit-detection', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(params),
    });
    return response.json();
  }
);

export const checkElectricalCompliance = createAsyncThunk(
  'contextual/checkCompliance',
  async (params: {
    equipmentType: string;
    specifications: any;
    location: { country: string; region: string };
  }) => {
    // TODO: Implement actual compliance checking API call
    const response = await fetch('/api/ai/compliance-check', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(params),
    });
    return response.json();
  }
);

const contextualSlice = createSlice({
  name: 'contextual',
  initialState,
  reducers: {
    setCurrentRequest: (state, action: PayloadAction<ContextualRequest | null>) => {
      state.currentRequest = action.payload;
    },
    addAIResponse: (state, action: PayloadAction<AIResponse>) => {
      state.aiResponses.push(action.payload);
    },
    addSafetyAlert: (state, action: PayloadAction<SafetyAlert>) => {
      state.safetyAlerts.unshift(action.payload);
    },
    acknowledgeSafetyAlert: (state, action: PayloadAction<string>) => {
      const alert = state.safetyAlerts.find(a => a.id === action.payload);
      if (alert) {
        alert.acknowledged = true;
      }
    },
    updateNLPSettings: (state, action: PayloadAction<Partial<typeof initialState.nlpSettings>>) => {
      state.nlpSettings = { ...state.nlpSettings, ...action.payload };
    },
    clearRequestHistory: (state) => {
      state.requestHistory = [];
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(processNaturalLanguageRequest.pending, (state) => {
        state.isProcessing = true;
        state.error = null;
      })
      .addCase(processNaturalLanguageRequest.fulfilled, (state, action) => {
        state.isProcessing = false;
        state.currentRequest = action.payload.request;
        state.requestHistory.push(action.payload.request);
        if (action.payload.response) {
          state.aiResponses.push(action.payload.response);
        }
      })
      .addCase(processNaturalLanguageRequest.rejected, (state, action) => {
        state.isProcessing = false;
        state.error = action.error.message || 'Failed to process request';
      })
      .addCase(detectCounterfeitProduct.fulfilled, (state, action) => {
        if (action.payload.isCounterfeit) {
          const alert: SafetyAlert = {
            id: Date.now().toString(),
            type: 'counterfeit_warning',
            severity: 'danger',
            title: 'Produit Contrefait Détecté',
            description: action.payload.description,
            recommendations: action.payload.recommendations,
            relatedStandards: action.payload.standards,
            timestamp: new Date().toISOString(),
            acknowledged: false,
          };
          state.safetyAlerts.unshift(alert);
        }
      })
      .addCase(checkElectricalCompliance.fulfilled, (state, action) => {
        if (!action.payload.compliant) {
          const alert: SafetyAlert = {
            id: Date.now().toString(),
            type: 'compliance_issue',
            severity: action.payload.severity,
            title: 'Non-Conformité Détectée',
            description: action.payload.description,
            recommendations: action.payload.recommendations,
            relatedStandards: action.payload.standards,
            timestamp: new Date().toISOString(),
            acknowledged: false,
          };
          state.safetyAlerts.unshift(alert);
        }
      });
  },
});

export const {
  setCurrentRequest,
  addAIResponse,
  addSafetyAlert,
  acknowledgeSafetyAlert,
  updateNLPSettings,
  clearRequestHistory,
  clearError,
} = contextualSlice.actions;

export default contextualSlice.reducer;
