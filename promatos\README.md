# ProMatos - Electrical Resource Sharing & Community Support

ProMatos is a comprehensive mobile application that connects users with electrical resources, professionals, and community support through AI-powered contextual intelligence. The app serves as a universal platform for electrical needs in French-speaking African markets, particularly Senegal.

## 🚀 Core Value Proposition

Transform any electrical need into immediate local action through a hybrid network combining AI contextual analysis, community of electricians, and local resource sharing.

## ✨ Key Features

### 1. Contextual Intelligence Engine
- Natural language processing for user requests (e.g., "I need a drill now in Dakar")
- Multi-dimensional analysis: location, urgency, user profile, weather, local standards
- Anti-counterfeit detection system for electrical products
- Local electrical standards compliance recommendations

### 2. Professional Network Features
- Real-time geolocation of available electricians
- Reputation system based on certifications and work quality
- Professional chat with media sharing (diagrams, photos, diagnostics)
- Emergency alert system for electrical situations
- Verified supplier deals sharing among professionals

### 3. Community Marketplace
- Tool lending/rental between individuals and professionals
- Electrical equipment exchange system (cables, components, tools)
- Alternative payment methods: time-banking, services, internal cryptocurrency
- "No-money" mode for users in financial difficulty

### 4. Accessibility Features
- Multilingual voice interface (French, Wolof, Arabic, other local languages)
- Offline functionality using mesh/Bluetooth for rural areas
- Simplified interface for seniors and beginners
- Compatibility with both basic phones and smartphones

### 5. Safety and Prevention
- Preventive alerts for planned power outages
- Electrical hazard detection (overload, potential short circuits)
- Localized safety guides and best practices
- Continuous electrical safety training modules

## 🛠 Technical Stack

- **Framework**: React Native with Expo
- **State Management**: Redux Toolkit with Redux Persist
- **Navigation**: React Navigation 6
- **UI Components**: Custom components with theme support
- **Offline Support**: Redux Persist + AsyncStorage
- **Location Services**: Expo Location
- **Internationalization**: Custom i18n with French, Wolof, Arabic, English
- **Development**: TypeScript for type safety

## 📱 Project Structure

```
promatos/
├── src/
│   ├── components/          # Reusable UI components
│   ├── contexts/           # React contexts (Theme, Language)
│   ├── navigation/         # Navigation configuration
│   ├── screens/           # Screen components
│   │   ├── auth/          # Authentication screens
│   │   └── ...            # Other screens
│   ├── services/          # API and external services
│   ├── store/             # Redux store configuration
│   │   └── slices/        # Redux slices
│   ├── types/             # TypeScript type definitions
│   └── utils/             # Utility functions
├── assets/                # Images, fonts, etc.
└── ...
```

## 🚀 Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn
- Expo CLI (`npm install -g @expo/cli`)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd promatos
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm start
```

4. Run on different platforms:
```bash
# Web
npm run web

# Android (requires Android Studio/emulator)
npm run android

# iOS (requires Xcode - macOS only)
npm run ios
```

## 🏗 Development Status

### ✅ Completed
- [x] Project setup with React Native + Expo
- [x] Core architecture with Redux Toolkit
- [x] Navigation structure with React Navigation
- [x] Theme system with light/dark/high-contrast modes
- [x] Internationalization system (French, Wolof, Arabic, English)
- [x] Authentication flow (Login/Register/Onboarding)
- [x] Basic screen structure
- [x] State management for all core features
- [x] Offline-first architecture foundation

### 🚧 In Progress
- [ ] Core Infrastructure Development
- [ ] Contextual Intelligence Engine
- [ ] Professional Network Features
- [ ] Community Marketplace
- [ ] Accessibility & Multilingual Support
- [ ] Safety & Prevention System
- [ ] Testing & Quality Assurance

## 🎯 Target Market

Primary focus on French-speaking African markets, starting with Senegal, with potential expansion to other Francophone African countries.

## 🔧 Configuration

### Environment Variables
Create a `.env` file in the root directory:
```
API_BASE_URL=https://api.promatos.com
GOOGLE_MAPS_API_KEY=your_google_maps_key
FIREBASE_CONFIG=your_firebase_config
```

### Theme Customization
Themes can be customized in `src/contexts/ThemeContext.tsx`:
- Light theme
- Dark theme  
- High contrast theme for accessibility

### Language Support
Add new languages in `src/contexts/LanguageContext.tsx`:
- French (fr) - Primary
- Wolof (wo) - Senegalese local language
- Arabic (ar) - Regional language
- English (en) - International

## 📱 Features Implementation Plan

### Phase 1: Core Foundation ✅
- Basic app structure
- Authentication system
- Navigation and routing
- Theme and internationalization

### Phase 2: Intelligence Engine 🚧
- Natural language processing
- Contextual analysis
- AI-powered recommendations
- Anti-counterfeit detection

### Phase 3: Professional Network 📋
- Electrician profiles and verification
- Real-time location tracking
- Professional chat system
- Emergency alert system

### Phase 4: Marketplace 📋
- Tool and equipment listings
- Rental/exchange system
- Alternative payment methods
- Transaction management

### Phase 5: Advanced Features 📋
- Voice interface
- Offline mesh networking
- Safety monitoring
- Training modules

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Support

For support and questions:
- Email: <EMAIL>
- Documentation: [docs.promatos.com](https://docs.promatos.com)
- Community: [community.promatos.com](https://community.promatos.com)

---

**ProMatos** - Connecting communities through intelligent electrical resource sharing 🔌⚡
